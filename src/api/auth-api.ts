import {
  GoogleAuthProvider,
  signInWithPopup,
  signInWithCustomToken,
} from "firebase/auth";
import { collection, getDocs, query, where } from "firebase/firestore";
import { httpsCallable } from "firebase/functions";

import { firebaseAuth, firestore, firebaseFunctions } from "@/root-context";
import { getTelegramWebAppData, isTelegramWebApp } from "@/utils/telegram-auth";

export const signInWithGoogle = async () => {
  const provider = new GoogleAuthProvider();

  try {
    await signInWithPopup(firebaseAuth, provider);
  } catch (error) {
    console.error("Error during sign in:", error);
  }
};

export const signOut = async () => {
  try {
    await firebaseAuth.signOut();
  } catch (error) {
    console.error("Error during sign out:", error);
  }
};

export const getCurrentUserId = () => firebaseAuth.currentUser?.uid;

export const signInWithTelegram = async () => {
  if (!isTelegramWebApp()) {
    throw new Error("This function can only be called from Telegram Web App");
  }

  try {
    // Get Telegram Web App data
    const telegramData = getTelegramWebAppData();

    if (!telegramData) {
      throw new Error("Failed to get Telegram data");
    }

    // Call Firebase function to authenticate
    const authenticateWithTelegram = httpsCallable(
      firebaseFunctions,
      "authenticateWithTelegram"
    );

    const result = await authenticateWithTelegram({
      initData: window.Telegram?.WebApp?.initData,
    });

    const { customToken } = result.data as { customToken: string };

    // Sign in with custom token
    await signInWithCustomToken(firebaseAuth, customToken);
  } catch (error) {
    console.error("Error during Telegram sign in:", error);
    throw error;
  }
};

export const getUserRole = async () => {
  try {
    const q = query(
      collection(firestore, "users"),
      where("id", "==", getCurrentUserId())
    );

    const adminSnapshot = await getDocs(q);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const admins: any[] = [];
    adminSnapshot.forEach((doc) => {
      admins.push({ id: doc.id, ...doc.data() });
    });
    return admins[0]?.role;
  } catch (error) {
    console.error("Error checking if current User is admin:", error);
    throw error;
  }
};

"use client";

import { useRootContext } from "@/root-context";
import { useTelegramWebApp } from "@/hooks/useTelegramWebApp";

export const TelegramTest = () => {
  const { currentUser } = useRootContext();
  const { isInTelegram, isInitialized, telegramData, isLoading, error, retry } =
    useTelegramWebApp();

  return (
    <div className="p-6 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-4">Telegram Integration Test</h3>

      <div className="space-y-4">
        {/* Loading State */}
        {isLoading && (
          <div className="flex items-center space-x-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-600 border-t-transparent"></div>
            <span className="text-blue-800">
              Initializing Telegram Web App...
            </span>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800 font-medium">❌ Error</p>
            <p className="text-red-700 text-sm mt-1">{error}</p>
            <button
              onClick={retry}
              className="mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
            >
              Retry
            </button>
          </div>
        )}

        {/* Status Information */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <strong>Is in Telegram Web App:</strong>{" "}
            <span className={isInTelegram ? "text-green-600" : "text-red-600"}>
              {isInTelegram ? "Yes" : "No"}
            </span>
          </div>
          <div>
            <strong>Is Initialized:</strong>{" "}
            <span
              className={isInitialized ? "text-green-600" : "text-orange-600"}
            >
              {isInitialized ? "Yes" : "No"}
            </span>
          </div>
        </div>

        <div>
          <strong>Current Firebase User:</strong>{" "}
          {currentUser ? currentUser.uid : "Not signed in"}
        </div>

        {/* Telegram Data */}
        {telegramData && (
          <div>
            <strong>Telegram Data:</strong>
            <pre className="mt-2 p-2 bg-gray-100 rounded text-sm overflow-auto">
              {JSON.stringify(telegramData, null, 2)}
            </pre>
          </div>
        )}

        {/* Raw Telegram WebApp Object */}
        {typeof window !== "undefined" && window.Telegram?.WebApp && (
          <div>
            <strong>Telegram WebApp Object:</strong>
            <pre className="mt-2 p-2 bg-gray-100 rounded text-sm overflow-auto">
              {JSON.stringify(
                {
                  initData: window.Telegram.WebApp.initData,
                  initDataUnsafe: window.Telegram.WebApp.initDataUnsafe,
                },
                null,
                2
              )}
            </pre>
          </div>
        )}

        <div className="text-sm text-gray-600">
          <p>
            <strong>Note:</strong> This component shows debug information about
            Telegram Web App integration. It will only show meaningful data when
            opened from within Telegram.
          </p>
        </div>
      </div>
    </div>
  );
};

export default TelegramTest;

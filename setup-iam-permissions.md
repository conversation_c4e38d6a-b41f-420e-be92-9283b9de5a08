# Fix Firebase Functions IAM Permissions for Custom Tokens

Based on the Firebase documentation (https://firebase.google.com/docs/auth/admin/create-custom-tokens), the error occurs because Firebase Functions doesn't have the necessary IAM permissions to create custom tokens.

## Root Cause

The error `Permission 'iam.serviceAccounts.signBlob' denied` happens because:

1. Firebase Functions runs with a default service account
2. This service account doesn't have permission to sign tokens
3. Custom token creation requires the `iam.serviceAccountTokenCreator` role

## Solution 1: Grant IAM Permissions (Recommended)

### Step 1: Get your Firebase project ID

```bash
firebase projects:list
# Or check your .firebaserc file
cat .firebaserc
```

### Step 2: Identify the service account

Firebase Functions uses this service account by default:

```
<project-id>@appspot.gserviceaccount.com
```

### Step 3: Grant the Service Account Token Creator role

```bash
# Replace YOUR_PROJECT_ID with your actual Firebase project ID
gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/iam.serviceAccountTokenCreator"
```

### Step 4: Grant the Service Account User role (if needed)

```bash
gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
    --member="serviceAccount:<EMAIL>" \
    --role="roles/iam.serviceAccountUser"
```

### Step 5: Verify the permissions

```bash
gcloud projects get-iam-policy YOUR_PROJECT_ID \
    --flatten="bindings[].members" \
    --format="table(bindings.role)" \
    --filter="bindings.members:<EMAIL>"
```

## Option 2: Alternative - Use Firebase Admin SDK with Service Account Key

If the above doesn't work, you can use a service account key file:

### Step 1: Generate a service account key

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to IAM & Admin > Service Accounts
3. Find your Firebase service account (usually `<EMAIL>`)
4. Click on it, go to "Keys" tab
5. Click "Add Key" > "Create new key" > "JSON"
6. Download the JSON file

### Step 2: Add the key to your Firebase Functions

1. Place the JSON file in your `functions` folder (e.g., `functions/serviceAccountKey.json`)
2. Add it to `.gitignore` to keep it secure
3. Update your Firebase Functions initialization

## Option 3: Use Firebase CLI to set up permissions

```bash
# Make sure you're logged in with the right account
firebase login

# Set the project
firebase use YOUR_PROJECT_ID

# Deploy functions (this should automatically set up permissions)
firebase deploy --only functions
```

## Verification

After applying any of these solutions, redeploy your functions:

```bash
cd functions
npm run build
cd ..
firebase deploy --only functions
```

Then test the Telegram authentication again. The error should be resolved.

## Security Note

- Never commit service account keys to version control
- Use environment variables or Firebase's built-in service account when possible
- The IAM permissions approach (Option 1) is more secure and recommended for production
